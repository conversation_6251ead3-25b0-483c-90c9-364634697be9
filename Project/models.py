from django.db import models

from Authentication.models import *



class PostIndustry(models.Model):
    name = models.CharField(max_length=250)

class Post(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    brand = models.ForeignKey(Brands, on_delete=models.CASCADE)
    title = models.CharField(max_length=1000)
    description = models.CharField(max_length=5000,null=True,blank=True,default='')
    location = models.CharField(max_length=255,null=True,blank=True,default='')
    is_private = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    is_video = models.BooleanField(default=False)
    is_text_post = models.BooleanField(default=False)
    is_approved = models.BooleanField(default=True)
    likes = models.IntegerField(default=0)
    dislikes = models.IntegerField(default=0)
    comments_count = models.IntegerField(default=0)
    report_count = models.IntegerField(default=0)
    is_banned = models.BooleanField(default=False)
    facebook = models.BooleanField(default=False)
    facebook_id = models.CharField(default='')
    mastadon = models.BooleanField(default=False)
    mastadon_id= models.CharField(default='')
    tiktok = models.BooleanField(default=False)
    tiktok_id = models.CharField(default='')
    instagram = models.BooleanField(default=False)
    instagram_id = models.CharField(default='')
    linkedin = models.BooleanField(default=False)
    linkedin_id = models.CharField(default='')
    pinterest = models.BooleanField(default=False)
    pinterest_id = models.CharField(default='')
    vimeo = models.BooleanField(default=False)
    vimeo_id = models.CharField(default='')
    youtube = models.BooleanField(default=False)
    youtube_id = models.CharField(default='')
    dailymotion = models.BooleanField(default=False)
    dailymotion_id = models.CharField(default='')
    twitter = models.BooleanField(default=False)
    twitter_id = models.CharField(default='')
    x = models.BooleanField(default=False)
    x_id = models.CharField(default='')
    tumblr = models.BooleanField(default=False)
    reddit = models.BooleanField(default=False)
    tumblr_id = models.CharField(default='')
    tagged_in = models.JSONField(default=list)
    scheduled_at = models.CharField(max_length=200, blank=True, null=True)
    is_scheduled = models.BooleanField(default=False)
    is_unscheduled = models.BooleanField(default=False)
    is_posted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    industry = models.ForeignKey(PostIndustry,null=True,blank=True,on_delete=models.CASCADE)
    shares = models.IntegerField(default=0)

class ReportPost(models.Model):
    reporting_user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE,related_name='reporting_user_post')
    reported_post = models.ForeignKey(Post,on_delete=models.CASCADE,related_name='reported_post')
    reason = models.TextField(default='') 
    created_at = models.DateTimeField(auto_now_add=True)

class PostFiles(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE)
    file = models.FileField(upload_to='post_files/')
    thumbnail = models.FileField(upload_to='post_files/',null=True,blank=True)
    is_video = models.BooleanField()

class PostFilesThumbnail(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE)
    post_file = models.ForeignKey(PostFiles, on_delete=models.CASCADE)
    file = models.FileField(upload_to='thumbnails/')




class Story(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    music = models.CharField(max_length=255, default='',null=True,blank=True)
    title = models.CharField(max_length=255, default='',null=True,blank=True)
    is_deleted = models.BooleanField(default=False)
    is_archieved = models.BooleanField(default=False)
    is_highlighted = models.BooleanField(default=False)
    created_on = models.DateTimeField(auto_now_add=True)
    
    @property
    def total_views(self):
        return StoryView.objects.filter(story=self).values('user').distinct().count()

class LikeStory(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    story = models.ForeignKey(Story, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

class StoryView(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    story = models.ForeignKey(Story, on_delete=models.CASCADE)
    viewed_on = models.DateTimeField(auto_now_add=True)

class StoryFiles(models.Model):
    story = models.ForeignKey(Story, on_delete=models.CASCADE)
    file = models.FileField(upload_to='story_files/')


class Follow(models.Model):
    from_user = models.ForeignKey(UserRegistration, related_name='following', on_delete=models.CASCADE)
    to_user = models.ForeignKey(UserRegistration, related_name='followers', on_delete=models.CASCADE)
    is_accepted = models.BooleanField(default=False)

class LikePost(models.Model):
    post = models.ForeignKey(Post,on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)

class DislikePost(models.Model):
    post = models.ForeignKey(Post,on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)

class SavedPost(models.Model):
    post = models.ForeignKey(Post,on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)


class Comment(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    comment_text = models.CharField(max_length=1000,default='')
    created_at = models.DateTimeField(auto_now_add=True)

class LikeComment(models.Model):
    comment = models.ForeignKey(Comment, on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

class CommentReply(models.Model):
    comment = models.ForeignKey(Comment, related_name='replies', on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    reply_text = models.CharField(max_length=1000, default='')
    created_at = models.DateTimeField(auto_now_add=True)

class LikeCommentReply(models.Model):
    comment_reply = models.ForeignKey(CommentReply, on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

class HidePost(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

class SavePost(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE)
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)


class Notification(models.Model):
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE,related_name='to_user')
    from_user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE,related_name='from_user')
    type = models.CharField(max_length=250,default='')
    post = models.ForeignKey(Post,on_delete=models.CASCADE,default='',null=True,blank=True)
    title = models.CharField(max_length=250,default='')
    message = models.CharField(max_length=250,default='')
    created_at = models.DateTimeField(auto_now_add=True)

class ChatMessage(models.Model):
    from_user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE,related_name='chat_from_user')
    to_user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE,related_name='chat_to_user')
    is_read = models.BooleanField(default=False)
    read_time = models.CharField(max_length=250,default='')
    type = models.CharField(max_length=250)
    file = models.FileField(upload_to='chat_files/',blank=True,null=True)
    message_id = models.CharField(max_length=250,default='')
    messages = models.TextField()
    deleted_by_from_user = models.BooleanField(default=False)
    deleted_by_to_user = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

class Highlight(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE, related_name='highlights')
    title = models.CharField(max_length=255)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    story = models.JSONField()
    added_at = models.DateTimeField(auto_now_add=True)

class HashTags(models.Model):
    name = models.CharField(max_length=550)
    posts = models.JSONField(blank=True,null=True)
    created_at = models.DateTimeField(auto_now_add=True)

class SocialPlatforms(models.Model):
    instagram = models.CharField(default='1')
    facebook = models.CharField(default='1')
    threads = models.CharField(default='1')
    youtube = models.CharField(default='1')
    linkedin = models.CharField(default='1')
    pinterest = models.CharField(default='1')
    vimeo = models.CharField(default='1')
    tiktok =models.CharField(default='1')
    tumblr = models.CharField(default='1')
    reddit = models.CharField(default='1')


class Feedback(models.Model):
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    app_using_frequency = models.CharField(max_length=250)
    stars = models.CharField(max_length=250)
    description = models.CharField(max_length=1000)
    file = models.FileField(upload_to='voice_feedback/',null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

#Fantacy 

class UserKYC(models.Model):
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    aadhar_number = models.CharField(max_length=250,null=True,blank=True)
    aadhar_front_image = models.FileField(upload_to='aadhar_front_image/',null=True,blank=True)
    aadhar_back_image = models.FileField(upload_to='aadhar_back_image/',null=True,blank=True)
    pan_number = models.CharField(max_length=250,null=True,blank=True)
    pan_image = models.FileField(upload_to='pan_image/',null=True,blank=True)
    name_as_per_pan = models.CharField(max_length=250,null=True,blank=True)
    is_rejected = models.BooleanField(default=False)
    is_pending = models.BooleanField(default=False)
    is_verified_pan = models.BooleanField(default=False)
    is_verified_aadhar = models.BooleanField(default=False)
    updated_at = models.DateTimeField(auto_now=True)

class MarkentCategories(models.Model):
    name = models.CharField(max_length=250)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class FantasyEvent(models.Model):
    #Foreign Keys
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    category = models.ForeignKey(MarkentCategories,on_delete=models.CASCADE)

    #event details
    event_name = models.CharField(max_length=250)
    maximum_joiners = models.CharField(max_length=250)
    minimum_followers = models.CharField(max_length=250)

    #social platform details
    selected_social_platforms = models.JSONField(default=list)
    language_preference = models.JSONField(default=list)

    #event time details
    registration_max_time = models.CharField(max_length=250)
    maximum_pool_running_days = models.CharField(max_length=250)

    #prize details
    event_prize = models.CharField(max_length=250)
    max_event_rank = models.CharField(max_length=250)

    #brand details
    brand_name = models.CharField(max_length=250)
    brand_logo = models.FileField(upload_to='markent_brand_logo/',null=True,blank=True)
    brand_description = models.TextField(default='')

    #content details 
    content_requirements = models.TextField(default='')
    product_description = models.TextField(default='')
    hashtags = models.JSONField(default=list)
    guidelines = models.TextField(default='')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class JoinerAdditionalDetails(models.Model):
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    location = models.JSONField(default=dict)
    language_preference = models.JSONField(default=list)
    industries_preference = models.JSONField(default=list)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class FantasyEventJoiner(models.Model):
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    fantasy_event = models.ForeignKey(FantasyEvent,on_delete=models.CASCADE)
    post = models.ForeignKey(Post,on_delete=models.CASCADE)
    is_post_approved = models.BooleanField(default=False)
    is_post_uploaded = models.BooleanField(default=False)
    current_rank = models.CharField(default='0',max_length=250)
    is_reward_claimed = models.BooleanField(default=False)
    is_eligible = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)


class UserWallet(models.Model):
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    balance = models.CharField(max_length=250)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class UserWalletTransaction(models.Model):
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    amount = models.CharField(max_length=250)
    transaction_type = models.CharField(max_length=250)
    transaction_id = models.CharField(max_length=250)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class UserBankDetails(models.Model):
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    rp_contact_id = models.CharField(max_length=250)
    bank_name = models.CharField(max_length=250)
    account_number = models.CharField(max_length=250)
    ifsc_code = models.CharField(max_length=250)
    is_verified_kyc = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)